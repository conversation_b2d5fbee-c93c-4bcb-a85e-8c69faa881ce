-- Update Row Level Security (RLS) policy for user-websites table
-- This migration ensures proper user isolation and security

-- First, ensure the user_id column exists and is properly configured
ALTER TABLE "user-websites"
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Create index for faster user lookups if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_user_websites_user_id
ON "user-websites" (user_id);

-- Enable RLS on the user-websites table
ALTER TABLE "user-websites" ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies that might conflict
DROP POLICY IF EXISTS "Enable read access for all users" ON "user-websites";
DROP POLICY IF EXISTS "Users can view their own websites" ON "user-websites";
DROP POLICY IF EXISTS "Users can insert their own websites" ON "user-websites";
DROP POLICY IF EXISTS "Users can update their own websites" ON "user-websites";
DROP POLICY IF EXISTS "Users can delete their own websites" ON "user-websites";

-- Create comprehensive RLS policies for user-websites table

-- Policy: Users can only view their own websites
CREATE POLICY "Users can view their own websites" ON "user-websites"
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can only insert websites for themselves
CREATE POLICY "Users can insert their own websites" ON "user-websites"
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only update their own websites
CREATE POLICY "Users can update their own websites" ON "user-websites"
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can only delete their own websites
CREATE POLICY "Users can delete their own websites" ON "user-websites"
  FOR DELETE USING (auth.uid() = user_id);

-- Add a comment to document the security model
COMMENT ON TABLE "user-websites" IS 'User websites with Row Level Security - users can only access their own sites';
COMMENT ON COLUMN "user-websites".user_id IS 'Reference to auth.users(id) - enforced by RLS policies';

-- Update any existing records that might have NULL user_id
-- This is a data migration step that should be run carefully
-- You may need to customize this based on your existing data structure

-- If you have a way to determine the correct user_id for existing records, update them here
-- For example, if you have a separate mapping or can derive it from other fields:
-- UPDATE "user-websites" SET user_id = (SELECT id FROM auth.users WHERE email = some_email_field) WHERE user_id IS NULL;

-- Alternatively, if you need to manually assign user_ids, you can do so through the Supabase dashboard

-- Verify the policies are in place
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'user-websites';

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'user-websites';
