# Row Level Security (RLS) Implementation for user-websites Table

## Overview

This document outlines the implementation of Row Level Security (RLS) for the `user-websites` table to ensure proper user isolation and data security. The implementation ensures that users can only access their own website records.

## Changes Made

### 1. Database Migration

**File**: `migrations/update_user_websites_rls.sql`

The migration script:
- Ensures the `user_id` column exists with proper foreign key relationship to `auth.users(id)`
- Enables Row Level Security on the `user-websites` table
- Creates comprehensive RLS policies for all operations (SELECT, INSERT, UPDATE, DELETE)
- Removes any conflicting existing policies
- Adds proper indexing for performance

### 2. RLS Policies Created

#### Policy: "Users can view their own websites"
```sql
CREATE POLICY "Users can view their own websites" ON "user-websites"
  FOR SELECT USING (auth.uid() = user_id);
```

#### Policy: "Users can insert their own websites"
```sql
CREATE POLICY "Users can insert their own websites" ON "user-websites"
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

#### Policy: "Users can update their own websites"
```sql
CREATE POLICY "Users can update their own websites" ON "user-websites"
  FOR UPDATE USING (auth.uid() = user_id);
```

#### Policy: "Users can delete their own websites"
```sql
CREATE POLICY "Users can delete their own websites" ON "user-websites"
  FOR DELETE USING (auth.uid() = user_id);
```

### 3. Application Code Updates

#### Client-Side Components Updated

**Files Modified**:
- `wp-ai-app/src/app/dashboard/page.tsx`
- `wp-ai-app-hisham/src/app/dashboard/page.tsx`
- `wp-ai-app/src/components/dashboard/domain/DomainSiteMappingStep.tsx`
- `wp-ai-app-hisham/src/components/domain/DomainSiteMappingStep.tsx`

**Changes**:
- Removed manual user_id filtering from client-side queries
- Added proper authentication checks before fetching data
- Added ordering by `created_at` for consistent results
- Updated useEffect dependencies to include user authentication state

**Before**:
```typescript
const { data, error } = await supabase
  .from('user-websites')
  .select('id, site_name, expiry_status, expiry_time');
```

**After**:
```typescript
// With RLS enabled, the database will automatically filter to user's sites
const { data, error } = await supabase
  .from('user-websites')
  .select('id, site_name, expiry_status, expiry_time')
  .order('created_at', { ascending: false });
```

#### API Routes Updated

**Files Modified**:
- `wp-ai-app/src/app/api/namecheap/route.ts`
- `wp-ai-app-hisham/src/app/api/namecheap/route.ts`

**Changes**:
- Added explicit `user_id` filtering as an additional security layer
- Maintained existing security checks in API routes that use `supabaseAdmin`

**Example**:
```typescript
const { data: site, error: siteError } = await supabase
  .from('user-websites')
  .select('site_name')
  .eq('id', siteId)
  .eq('user_id', userId) // Ensure user owns the site
  .single();
```

## Security Model

### Defense in Depth

The implementation follows a defense-in-depth security model:

1. **Database Level (RLS)**: PostgreSQL Row Level Security policies enforce access control at the database level
2. **Application Level**: API routes include explicit user_id filtering for additional security
3. **Authentication Level**: Supabase authentication ensures only authenticated users can access the system

### Benefits

1. **Automatic Filtering**: Client-side queries automatically filter to the authenticated user's data
2. **Simplified Code**: Removes the need for manual user_id filtering in most client-side code
3. **Security by Default**: Even if application code has bugs, the database prevents unauthorized access
4. **Performance**: Database-level filtering is more efficient than application-level filtering
5. **Consistency**: Ensures consistent security across all application components

## Migration Instructions

### 1. Apply Database Migration

Run the migration script in your Supabase SQL editor:

```bash
# For wp-ai-app
psql -f wp-ai-app/migrations/update_user_websites_rls.sql

# For wp-ai-app-hisham  
psql -f wp-ai-app-hisham/migrations/update_user_websites_rls.sql
```

### 2. Update Existing Data

If you have existing records in the `user-websites` table without `user_id` values, you'll need to populate them. This depends on your existing data structure.

### 3. Verify Implementation

After applying the migration, verify the policies are active:

```sql
-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'user-websites';

-- Check policies exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'user-websites';
```

## Testing

### 1. Test User Isolation

1. Create two test users
2. Create websites for each user
3. Verify each user can only see their own websites
4. Attempt to access another user's website by ID (should fail)

### 2. Test API Endpoints

1. Test domain mapping with valid site IDs
2. Test domain mapping with invalid/unauthorized site IDs
3. Verify proper error messages for unauthorized access

## Troubleshooting

### Common Issues

1. **"permission denied for table user-websites"**: RLS is enabled but no policies allow access
2. **Empty results**: User is not authenticated or user_id is not set on records
3. **Policy conflicts**: Multiple policies with conflicting rules

### Debugging Queries

```sql
-- Check current user
SELECT auth.uid();

-- Check user-websites records with user_id
SELECT id, site_name, user_id FROM "user-websites";

-- Test policy manually
SELECT * FROM "user-websites" WHERE auth.uid() = user_id;
```

## Future Considerations

1. **Audit Logging**: Consider adding audit trails for sensitive operations
2. **Role-Based Access**: Extend RLS for admin users or shared access scenarios
3. **Performance Monitoring**: Monitor query performance with RLS enabled
4. **Backup Policies**: Ensure backup/restore procedures account for RLS policies
